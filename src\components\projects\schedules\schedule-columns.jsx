import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Edit, Copy, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/projects/data-table/data-table-column-header";
import { ValidatedInput } from "@/components/projects/validated-input";

const DAYS_OF_WEEK = [
  { key: 'monday', label: 'Mon' },
  { key: 'tuesday', label: 'Tue' },
  { key: 'wednesday', label: 'Wed' },
  { key: 'thursday', label: 'Thu' },
  { key: 'friday', label: 'Fri' },
  { key: 'saturday', label: 'Sat' },
  { key: 'sunday', label: 'Sun' },
];

export function createScheduleColumns(
  onEdit,
  onDuplicate,
  onDelete,
  onCellEdit,
  getEffectiveValue
) {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => {
        const value = getEffectiveValue(row.original, "name");
        return (
          <ValidatedInput
            value={value}
            onChange={(newValue) => onCellEdit(row.original.id, "name", newValue)}
            placeholder="Enter schedule name"
            className="border-0 p-0 h-auto bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        );
      },
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      cell: ({ row }) => {
        const value = getEffectiveValue(row.original, "description");
        return (
          <ValidatedInput
            value={value}
            onChange={(newValue) => onCellEdit(row.original.id, "description", newValue)}
            placeholder="Enter description"
            className="border-0 p-0 h-auto bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        );
      },
    },
    {
      accessorKey: "time",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Time" />
      ),
      cell: ({ row }) => {
        const value = getEffectiveValue(row.original, "time");
        return (
          <ValidatedInput
            value={value}
            onChange={(newValue) => onCellEdit(row.original.id, "time", newValue)}
            placeholder="HH:MM"
            pattern="^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
            className="border-0 p-0 h-auto bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        );
      },
    },
    {
      accessorKey: "days",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Days" />
      ),
      cell: ({ row }) => {
        const daysValue = getEffectiveValue(row.original, "days");
        let activeDays = [];
        
        try {
          activeDays = typeof daysValue === 'string' ? JSON.parse(daysValue) : daysValue || [];
        } catch (e) {
          activeDays = [];
        }

        return (
          <div className="flex flex-wrap gap-1">
            {DAYS_OF_WEEK.map((day) => (
              <Badge
                key={day.key}
                variant={activeDays.includes(day.key) ? "default" : "outline"}
                className="text-xs"
              >
                {day.label}
              </Badge>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: "sceneCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Scenes" />
      ),
      cell: ({ row }) => {
        const count = row.original.sceneCount || 0;
        return (
          <Badge variant="secondary" className="text-xs">
            {count} scene{count !== 1 ? 's' : ''}
          </Badge>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const schedule = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(schedule)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDuplicate(schedule.id)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onDelete(schedule.id)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
